{"bet365.com": {"url": "https://bet365.com", "name": "bet365.com", "timestamp": "2025-07-30T08:38:50.157688", "status": "success", "vulnerabilities": [], "page_size": 4512, "scan_duration": 0.28661298751831055}, "betway.com": {"url": "https://betway.com", "name": "betway.com", "timestamp": "2025-07-30T08:38:50.158266", "status": "success", "vulnerabilities": [], "page_size": 296198, "scan_duration": 0.8310983180999756}, "caesars.com": {"url": "https://caesars.com", "name": "caesars.com", "timestamp": "2025-07-30T08:38:50.989918", "status": "success", "vulnerabilities": [], "page_size": 911, "scan_duration": 0.1489095687866211}, "pokerstars.com": {"url": "https://pokerstars.com", "name": "pokerstars.com", "timestamp": "2025-07-30T08:38:50.444628", "status": "success", "vulnerabilities": [{"severity": "HIGH", "category": "Weak RNG", "description": "Potentially predictable random number generation", "evidence": "new Date().getTime()", "recommendation": "Use cryptographically secure random number generators"}], "page_size": 114617, "scan_duration": 1.4184083938598633}, "draftkings.com": {"url": "https://draftkings.com", "name": "draftkings.com", "timestamp": "2025-07-30T08:38:51.139104", "status": "success", "vulnerabilities": [], "page_size": 590190, "scan_duration": 0.97347092628479}, "888casino.com": {"url": "https://888casino.com", "name": "888casino.com", "timestamp": "2025-07-30T08:38:50.158957", "status": "success", "vulnerabilities": [{"severity": "HIGH", "category": "Weak RNG", "description": "Potentially predictable random number generation", "evidence": "new Date().getTime()", "recommendation": "Use cryptographically secure random number generators"}, {"severity": "HIGH", "category": "Weak RNG", "description": "Potentially predictable random number generation", "evidence": "Date.now()", "recommendation": "Use cryptographically secure random number generators"}, {"severity": "CRITICAL", "category": "Hardcoded Credentials", "description": "Hardcoded credentials found in source code", "evidence": "token: 'MTo2NEZidVRiSVM5YVFVSXdCMGNXb01BOkFVRXNxa2...", "recommendation": "Remove hardcoded credentials and use environment variables"}, {"severity": "HIGH", "category": "XSS Vulnerability", "description": "Potential Cross-Site Scripting vulnerability", "evidence": "innerHTML = el1 + ' > ' + el2 + (el3_text ?  ' > ' + el3 : '')", "recommendation": "Sanitize user input and use safe DOM manipulation"}, {"severity": "HIGH", "category": "XSS Vulnerability", "description": "Potential Cross-Site Scripting vulnerability", "evidence": "eval(", "recommendation": "Sanitize user input and use safe DOM manipulation"}], "page_size": 4170908, "scan_duration": 3.139833927154541}, "mgm.com": {"url": "https://mgm.com", "name": "mgm.com", "timestamp": "2025-07-30T08:38:52.113137", "status": "success", "vulnerabilities": [{"severity": "HIGH", "category": "Weak RNG", "description": "Potentially predictable random number generation", "evidence": "new Date().getTime()", "recommendation": "Use cryptographically secure random number generators"}], "page_size": 311770, "scan_duration": 1.287536859512329}, "fanduel.com": {"url": "https://fanduel.com", "name": "fanduel.com", "timestamp": "2025-07-30T08:38:51.863490", "status": "success", "vulnerabilities": [], "page_size": 409995, "scan_duration": 1.6626124382019043}, "betrivers.com": {"url": "https://betrivers.com", "name": "betrivers.com", "timestamp": "2025-07-30T08:38:53.401143", "status": "success", "vulnerabilities": [{"severity": "HIGH", "category": "Weak RNG", "description": "Potentially predictable random number generation", "evidence": "new Date().getTime()", "recommendation": "Use cryptographically secure random number generators"}], "page_size": 12583, "scan_duration": 0.25434017181396484}, "borgata.com": {"url": "https://borgata.com", "name": "borgata.com", "timestamp": "2025-07-30T08:38:53.300543", "status": "connection_error", "vulnerabilities": [], "page_size": 0, "scan_duration": 0.41057372093200684}, "betfair.com": {"url": "https://betfair.com", "name": "betfair.com", "timestamp": "2025-07-30T08:38:53.711378", "status": "success", "vulnerabilities": [], "page_size": 2346, "scan_duration": 0.19474458694458008}, "paddy-power.com": {"url": "https://paddy-power.com", "name": "paddy-power.com", "timestamp": "2025-07-30T08:38:53.906422", "status": "connection_error", "vulnerabilities": [], "page_size": 0, "scan_duration": 0.5678024291992188}, "williamhill.com": {"url": "https://williamhill.com", "name": "williamhill.com", "timestamp": "2025-07-30T08:38:53.656104", "status": "success", "vulnerabilities": [], "page_size": 107359, "scan_duration": 1.022528886795044}, "ladbrokes.com": {"url": "https://ladbrokes.com", "name": "ladbrokes.com", "timestamp": "2025-07-30T08:38:54.474516", "status": "success", "vulnerabilities": [{"severity": "HIGH", "category": "Weak RNG", "description": "Potentially predictable random number generation", "evidence": "new Date().getTime()", "recommendation": "Use cryptographically secure random number generators"}], "page_size": 9527, "scan_duration": 0.6030433177947998}, "unibet.com": {"url": "https://unibet.com", "name": "unibet.com", "timestamp": "2025-07-30T08:38:53.526579", "status": "success", "vulnerabilities": [], "page_size": 119306, "scan_duration": 3.0142204761505127}}