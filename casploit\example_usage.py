#!/usr/bin/env python3
"""
Example Usage Script for Slot Game Security Scanner
Demonstrates how to use the scanner for different scenarios
"""

import os
import sys
from slot_security_scanner import SlotSecurityScanner
from payload_tester import PayloadTester

def scan_local_casino_files():
    """Example: Scan local casino game files"""
    print("="*60)
    print("EXAMPLE 1: Scanning Local Casino Files")
    print("="*60)
    
    # Example local files (you would replace with your actual files)
    local_files = [
        'casino.js',
        'slots.html',
        'poker.php',
        'blackjack.js'
    ]
    
    # Filter to only existing files
    existing_files = [f for f in local_files if os.path.exists(f)]
    
    if not existing_files:
        print("No local casino files found. Creating example...")
        create_example_vulnerable_file()
        existing_files = ['vulnerable_casino.js']
    
    scanner = SlotSecurityScanner(local_files=existing_files)
    scanner.scan_local_files()
    scanner.generate_report()

def scan_casino_website():
    """Example: Scan a casino website"""
    print("\n" + "="*60)
    print("EXAMPLE 2: Scanning Casino Website")
    print("="*60)
    
    # Example casino website (replace with actual URL)
    target_url = "https://example-casino.com"
    
    print(f"Target URL: {target_url}")
    print("Note: This is an example. Replace with your actual casino URL.")
    
    scanner = SlotSecurityScanner(target_url=target_url)
    
    # This would scan the actual website if it existed
    # scanner.scan_web_target()
    # scanner.generate_report()
    
    print("Website scanning skipped (example URL)")

def advanced_payload_testing():
    """Example: Advanced payload testing"""
    print("\n" + "="*60)
    print("EXAMPLE 3: Advanced Payload Testing")
    print("="*60)
    
    target_url = "https://example-casino.com"
    
    # Define API endpoints to test
    endpoints = {
        'login': f"{target_url}/api/login",
        'bet': f"{target_url}/api/bet",
        'balance': f"{target_url}/api/balance",
        'withdraw': f"{target_url}/api/withdraw",
        'deposit': f"{target_url}/api/deposit",
        'spin': f"{target_url}/api/spin",
        'jackpot': f"{target_url}/api/jackpot"
    }
    
    print("Endpoints to test:")
    for name, url in endpoints.items():
        print(f"  {name}: {url}")
    
    # This would run actual payload testing if the endpoints existed
    # tester = PayloadTester(target_url)
    # vulnerabilities = tester.run_comprehensive_test(endpoints)
    
    print("Payload testing skipped (example URLs)")

def create_example_vulnerable_file():
    """Create an example vulnerable casino file for demonstration"""
    vulnerable_code = '''
// Example Vulnerable Casino Game Code
class VulnerableCasino {
    constructor() {
        this.balance = 1000;
        this.apiKey = "sk_live_123456789"; // VULNERABILITY: Hardcoded API key
    }
    
    // VULNERABILITY: Client-side balance validation
    placeBet(amount) {
        if (this.balance >= amount) {
            this.balance -= amount;
            return this.spinSlots();
        }
        return false;
    }
    
    // VULNERABILITY: Predictable RNG
    spinSlots() {
        const seed = new Date().getTime(); // VULNERABILITY: Predictable seed
        const random = Math.random(); // VULNERABILITY: Weak RNG
        
        if (random > 0.5) {
            const winAmount = Math.floor(random * 1000);
            this.balance += winAmount; // VULNERABILITY: Client-side balance update
            return { win: true, amount: winAmount };
        }
        return { win: false, amount: 0 };
    }
    
    // VULNERABILITY: Client-side withdrawal logic
    withdraw(amount) {
        if (this.balance >= amount) {
            this.balance -= amount;
            // VULNERABILITY: No server-side verification
            this.sendWithdrawal(amount);
        }
    }
    
    // VULNERABILITY: Exposed API endpoint construction
    sendWithdrawal(amount) {
        const endpoint = "https://api.casino.com/withdraw?amount=" + amount;
        fetch(endpoint); // VULNERABILITY: No authentication
    }
    
    // VULNERABILITY: XSS vulnerability
    displayMessage(message) {
        document.getElementById('messages').innerHTML = message; // VULNERABILITY: XSS
    }
    
    // VULNERABILITY: Race condition potential
    async quickBet() {
        const result1 = this.placeBet(100);
        const result2 = this.placeBet(100); // VULNERABILITY: Multiple simultaneous bets
        return [result1, result2];
    }
}

// VULNERABILITY: Global variables
var userBalance = 1000;
var gameSecret = "casino_secret_123";

// VULNERABILITY: Eval usage
function processGameCommand(command) {
    eval(command); // VULNERABILITY: Code injection
}
'''
    
    with open('vulnerable_casino.js', 'w') as f:
        f.write(vulnerable_code)
    
    print("Created example vulnerable casino file: vulnerable_casino.js")

def demonstrate_configuration():
    """Demonstrate configuration customization"""
    print("\n" + "="*60)
    print("EXAMPLE 4: Configuration Customization")
    print("="*60)
    
    print("You can customize the scanner by editing scanner_config.json:")
    print()
    print("1. Add new vulnerability patterns:")
    print('   "custom_patterns": ["your_pattern_here"]')
    print()
    print("2. Modify scan settings:")
    print('   "timeout": 15,')
    print('   "max_threads": 10')
    print()
    print("3. Add custom payloads:")
    print('   "custom_sqli_payloads": ["your_payload"]')
    print()
    print("4. Adjust severity levels:")
    print('   "severity_levels": { "CUSTOM": {...} }')

def main():
    """Main demonstration function"""
    print("🎰 Slot Game Security Scanner - Usage Examples")
    print("=" * 60)
    
    print("\nThis script demonstrates various ways to use the security scanner:")
    print("1. Scanning local casino files")
    print("2. Scanning casino websites") 
    print("3. Advanced payload testing")
    print("4. Configuration customization")
    
    print("\n" + "⚠️  LEGAL NOTICE" + "⚠️")
    print("Only scan websites and applications you own or have permission to test!")
    print("Unauthorized scanning may be illegal in your jurisdiction.")
    
    # Run examples
    scan_local_casino_files()
    scan_casino_website()
    advanced_payload_testing()
    demonstrate_configuration()
    
    print("\n" + "="*60)
    print("EXAMPLES COMPLETED")
    print("="*60)
    print("\nTo run actual scans:")
    print("1. python slot_security_scanner.py -u https://your-casino.com")
    print("2. python slot_security_scanner.py -f your_casino_files.js")
    print("3. Edit scanner_config.json for custom settings")
    
    # Clean up example file
    if os.path.exists('vulnerable_casino.js'):
        os.remove('vulnerable_casino.js')
        print("\nCleaned up example file.")

if __name__ == "__main__":
    main()
