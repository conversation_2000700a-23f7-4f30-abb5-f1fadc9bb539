#!/usr/bin/env python3
"""
Batch Casino Security Scanner
Scans multiple popular casino sites for security vulnerabilities
"""

import requests
import re
import json
import time
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import urllib.parse
from urllib.parse import urljoin, urlparse

class BatchCasinoScanner:
    def __init__(self):
        self.results = {}
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # Top 15 most popular casino sites
        self.casino_sites = [
            "https://bet365.com",
            "https://betway.com", 
            "https://888casino.com",
            "https://pokerstars.com",
            "https://caesars.com",
            "https://draftkings.com",
            "https://fanduel.com",
            "https://mgm.com",
            "https://borgata.com",
            "https://betrivers.com",
            "https://unibet.com",
            "https://williamhill.com",
            "https://betfair.com",
            "https://paddy-power.com",
            "https://ladbrokes.com"
        ]
    
    def scan_single_casino(self, casino_url):
        """Scan a single casino for vulnerabilities"""
        casino_name = urlparse(casino_url).netloc
        print(f"🎰 Scanning {casino_name}...")
        
        vulnerabilities = []
        scan_info = {
            'url': casino_url,
            'name': casino_name,
            'timestamp': datetime.now().isoformat(),
            'status': 'unknown',
            'vulnerabilities': [],
            'page_size': 0,
            'scan_duration': 0
        }
        
        start_time = time.time()
        
        try:
            # Get main page with timeout
            response = self.session.get(casino_url, timeout=15)
            content = response.text
            scan_info['page_size'] = len(content)
            scan_info['status'] = 'success'
            
            print(f"  ✅ {casino_name}: Retrieved {len(content)} characters")
            
            # Quick vulnerability checks
            vulnerabilities.extend(self.check_weak_rng(content, casino_name))
            vulnerabilities.extend(self.check_client_validation(content, casino_name))
            vulnerabilities.extend(self.check_credentials(content, casino_name))
            vulnerabilities.extend(self.check_xss(content, casino_name))
            vulnerabilities.extend(self.check_protocol_security(casino_url, casino_name))
            
            scan_info['vulnerabilities'] = vulnerabilities
            
        except requests.exceptions.Timeout:
            print(f"  ⏰ {casino_name}: Timeout after 15 seconds")
            scan_info['status'] = 'timeout'
        except requests.exceptions.ConnectionError:
            print(f"  ❌ {casino_name}: Connection failed")
            scan_info['status'] = 'connection_error'
        except Exception as e:
            print(f"  ❌ {casino_name}: Error - {str(e)}")
            scan_info['status'] = 'error'
            scan_info['error'] = str(e)
        
        scan_info['scan_duration'] = time.time() - start_time
        return casino_name, scan_info
    
    def check_weak_rng(self, content, casino_name):
        """Check for weak random number generation"""
        vulnerabilities = []
        weak_rng_patterns = [
            r'Math\.random\(\)',
            r'new Date\(\)\.getTime\(\)',
            r'Date\.now\(\)',
            r'Math\.floor\(Math\.random\(\)\s*\*\s*\d+\)',
        ]
        
        for pattern in weak_rng_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                vulnerabilities.append({
                    'severity': 'HIGH',
                    'category': 'Weak RNG',
                    'description': 'Potentially predictable random number generation',
                    'evidence': match.group()[:100],  # Limit evidence length
                    'recommendation': 'Use cryptographically secure random number generators'
                })
                break  # Only report first instance per pattern
        
        return vulnerabilities
    
    def check_client_validation(self, content, casino_name):
        """Check for client-side validation issues"""
        vulnerabilities = []
        validation_patterns = [
            r'balance\s*[><=]+\s*bet',
            r'if\s*\(\s*bet\s*[><=]',
            r'balance\s*-=\s*bet',
            r'balance\s*\+=\s*win',
            r'localStorage\.setItem.*balance',
        ]
        
        for pattern in validation_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                vulnerabilities.append({
                    'severity': 'CRITICAL',
                    'category': 'Client-Side Validation',
                    'description': 'Critical game logic handled on client-side',
                    'evidence': match.group()[:100],
                    'recommendation': 'Move all balance and bet validation to server-side'
                })
                break  # Only report first instance per pattern
        
        return vulnerabilities
    
    def check_credentials(self, content, casino_name):
        """Check for hardcoded credentials"""
        vulnerabilities = []
        credential_patterns = [
            r'password\s*[:=]\s*[\'"][^\'"]{3,}[\'"]',
            r'api_key\s*[:=]\s*[\'"][^\'"]+[\'"]',
            r'secret\s*[:=]\s*[\'"][^\'"]+[\'"]',
            r'token\s*[:=]\s*[\'"][^\'"]+[\'"]',
        ]
        
        for pattern in credential_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                vulnerabilities.append({
                    'severity': 'CRITICAL',
                    'category': 'Hardcoded Credentials',
                    'description': 'Hardcoded credentials found in source code',
                    'evidence': match.group()[:50] + "...",  # Truncate sensitive data
                    'recommendation': 'Remove hardcoded credentials and use environment variables'
                })
                break  # Only report first instance per pattern
        
        return vulnerabilities
    
    def check_xss(self, content, casino_name):
        """Check for XSS vulnerabilities"""
        vulnerabilities = []
        xss_patterns = [
            r'innerHTML\s*=\s*[^;]+',
            r'document\.write\s*\(',
            r'eval\s*\(',
        ]
        
        for pattern in xss_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                vulnerabilities.append({
                    'severity': 'HIGH',
                    'category': 'XSS Vulnerability',
                    'description': 'Potential Cross-Site Scripting vulnerability',
                    'evidence': match.group()[:100],
                    'recommendation': 'Sanitize user input and use safe DOM manipulation'
                })
                break  # Only report first instance per pattern
        
        return vulnerabilities
    
    def check_protocol_security(self, url, casino_name):
        """Check protocol security"""
        vulnerabilities = []
        parsed_url = urlparse(url)
        
        if parsed_url.scheme != 'https':
            vulnerabilities.append({
                'severity': 'HIGH',
                'category': 'Insecure Protocol',
                'description': 'Site not using HTTPS',
                'evidence': f'Protocol: {parsed_url.scheme}',
                'recommendation': 'Implement HTTPS with valid SSL certificate'
            })
        
        return vulnerabilities
    
    def run_batch_scan(self, max_workers=5):
        """Run security scan on all casino sites"""
        print("🎰 Batch Casino Security Scanner")
        print("="*60)
        print(f"Scanning {len(self.casino_sites)} popular casino sites...")
        print("="*60)
        
        # Use ThreadPoolExecutor for concurrent scanning
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all scan jobs
            future_to_casino = {
                executor.submit(self.scan_single_casino, casino): casino 
                for casino in self.casino_sites
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_casino):
                casino_name, scan_result = future.result()
                self.results[casino_name] = scan_result
        
        # Generate comprehensive report
        self.generate_batch_report()
    
    def generate_batch_report(self):
        """Generate comprehensive batch scan report"""
        print("\n" + "="*60)
        print("BATCH SCAN RESULTS")
        print("="*60)
        
        total_sites = len(self.casino_sites)
        successful_scans = sum(1 for r in self.results.values() if r['status'] == 'success')
        total_vulnerabilities = sum(len(r['vulnerabilities']) for r in self.results.values())
        
        print(f"📊 SCAN SUMMARY:")
        print(f"  Total Sites Scanned: {total_sites}")
        print(f"  Successful Scans: {successful_scans}")
        print(f"  Failed Scans: {total_sites - successful_scans}")
        print(f"  Total Vulnerabilities Found: {total_vulnerabilities}")
        
        # Group vulnerabilities by severity
        severity_counts = {'CRITICAL': 0, 'HIGH': 0, 'MEDIUM': 0, 'LOW': 0}
        
        print(f"\n🔍 VULNERABILITY BREAKDOWN:")
        for casino_name, result in self.results.items():
            if result['vulnerabilities']:
                print(f"\n🎰 {casino_name}:")
                for vuln in result['vulnerabilities']:
                    severity = vuln['severity']
                    severity_counts[severity] += 1
                    print(f"  🔴 {severity}: {vuln['category']} - {vuln['description']}")
            else:
                if result['status'] == 'success':
                    print(f"\n✅ {casino_name}: No vulnerabilities detected")
                else:
                    print(f"\n❌ {casino_name}: Scan failed ({result['status']})")
        
        print(f"\n📈 OVERALL SEVERITY SUMMARY:")
        for severity, count in severity_counts.items():
            if count > 0:
                print(f"  {severity}: {count}")
        
        # Save detailed report
        report_file = f"batch_casino_scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        # Security recommendations
        print(f"\n🛡️ SECURITY RECOMMENDATIONS:")
        recommendations = [
            "🔒 Implement server-side validation for all game logic",
            "🎲 Use cryptographically secure random number generators", 
            "🛡️ Add proper authentication and authorization",
            "🔍 Implement comprehensive input validation",
            "📊 Add security monitoring and logging",
            "⚡ Implement rate limiting for API endpoints",
            "🔐 Ensure HTTPS is used for all communications",
            "🎯 Conduct regular security audits and penetration testing"
        ]
        
        for rec in recommendations:
            print(f"  {rec}")

def main():
    scanner = BatchCasinoScanner()
    scanner.run_batch_scan(max_workers=3)  # Limit concurrent connections

if __name__ == "__main__":
    main()
