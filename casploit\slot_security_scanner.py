#!/usr/bin/env python3
"""
Slot Game Security Scanner
A comprehensive security scanner for slot game websites and code
Identifies common vulnerabilities in gambling platforms
"""

import requests
import re
import json
import time
import random
import hashlib
import urllib.parse
from urllib.parse import urljoin, urlparse
import argparse
import sys
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor
import ssl
import socket

class SlotSecurityScanner:
    def __init__(self, target_url=None, local_files=None):
        self.target_url = target_url
        self.local_files = local_files or []
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.vulnerabilities = []
        self.scan_results = {
            'timestamp': datetime.now().isoformat(),
            'target': target_url,
            'vulnerabilities': [],
            'recommendations': []
        }
        
    def log_vulnerability(self, severity, category, description, evidence=None, recommendation=None):
        """Log a discovered vulnerability"""
        vuln = {
            'severity': severity,
            'category': category,
            'description': description,
            'evidence': evidence,
            'recommendation': recommendation,
            'timestamp': datetime.now().isoformat()
        }
        self.vulnerabilities.append(vuln)
        self.scan_results['vulnerabilities'].append(vuln)
        
        # Color coding for terminal output
        colors = {
            'CRITICAL': '\033[91m',  # Red
            'HIGH': '\033[93m',      # Yellow
            'MEDIUM': '\033[94m',    # Blue
            'LOW': '\033[92m',       # Green
            'END': '\033[0m'         # Reset
        }
        
        print(f"{colors.get(severity, '')}{severity}: {category} - {description}{colors['END']}")
        if evidence:
            print(f"  Evidence: {evidence}")
        if recommendation:
            print(f"  Recommendation: {recommendation}")
        print()

    def scan_rng_vulnerabilities(self, content):
        """Scan for Random Number Generator vulnerabilities"""
        print("[+] Scanning for RNG vulnerabilities...")
        
        # Check for weak RNG patterns
        weak_rng_patterns = [
            r'Math\.random\(\)',
            r'new Date\(\)\.getTime\(\)',
            r'Date\.now\(\)',
            r'Math\.floor\(Math\.random\(\)\s*\*\s*\d+\)',
            r'parseInt\(Math\.random\(\)',
            r'seed\s*=\s*\d+',
            r'srand\(',
            r'rand\(\)',
            r'random\.randint\(',
            r'random\.choice\(',
        ]
        
        for pattern in weak_rng_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'HIGH',
                    'Weak RNG',
                    f'Potentially predictable random number generation detected',
                    match.group(),
                    'Use cryptographically secure random number generators (crypto.getRandomValues(), secrets module)'
                )

    def scan_client_side_validation(self, content):
        """Scan for client-side validation vulnerabilities"""
        print("[+] Scanning for client-side validation issues...")
        
        # Check for client-side balance/bet validation
        validation_patterns = [
            r'balance\s*[><=]+\s*bet',
            r'if\s*\(\s*bet\s*[><=]',
            r'balance\s*-=\s*bet',
            r'balance\s*\+=\s*win',
            r'localStorage\.setItem.*balance',
            r'sessionStorage\.setItem.*balance',
            r'document\.cookie.*balance',
        ]
        
        for pattern in validation_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'CRITICAL',
                    'Client-Side Validation',
                    'Critical game logic handled on client-side',
                    match.group(),
                    'Move all balance and bet validation to server-side with proper authentication'
                )

    def scan_api_endpoints(self, content):
        """Scan for exposed API endpoints"""
        print("[+] Scanning for API endpoint vulnerabilities...")
        
        # Look for API endpoints
        api_patterns = [
            r'/api/[a-zA-Z0-9_/]+',
            r'fetch\([\'"]([^\'"]+)[\'"]',
            r'axios\.[get|post|put|delete]+\([\'"]([^\'"]+)[\'"]',
            r'XMLHttpRequest.*open\([\'"][GET|POST][\'"],\s*[\'"]([^\'"]+)[\'"]',
            r'endpoint\s*[:=]\s*[\'"]([^\'"]+)[\'"]',
        ]
        
        endpoints = set()
        for pattern in api_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                endpoint = match.group(1) if match.groups() else match.group()
                endpoints.add(endpoint)
        
        # Test endpoints for common vulnerabilities
        for endpoint in endpoints:
            if self.target_url:
                self.test_api_endpoint(endpoint)

    def test_api_endpoint(self, endpoint):
        """Test individual API endpoint for vulnerabilities"""
        if not endpoint.startswith('http'):
            endpoint = urljoin(self.target_url, endpoint)
        
        try:
            # Test for authentication bypass
            response = self.session.get(endpoint)
            if response.status_code == 200:
                self.log_vulnerability(
                    'MEDIUM',
                    'Exposed API Endpoint',
                    f'API endpoint accessible without authentication: {endpoint}',
                    f'Status: {response.status_code}',
                    'Implement proper authentication and authorization'
                )
            
            # Test for SQL injection
            sqli_payloads = ["'", "1' OR '1'='1", "'; DROP TABLE users; --"]
            for payload in sqli_payloads:
                test_url = f"{endpoint}?id={payload}"
                try:
                    response = self.session.get(test_url, timeout=5)
                    if any(error in response.text.lower() for error in ['sql', 'mysql', 'sqlite', 'postgresql']):
                        self.log_vulnerability(
                            'CRITICAL',
                            'SQL Injection',
                            f'Potential SQL injection vulnerability in {endpoint}',
                            f'Payload: {payload}',
                            'Use parameterized queries and input validation'
                        )
                        break
                except:
                    pass
                    
        except Exception as e:
            pass

    def scan_authentication_flaws(self, content):
        """Scan for authentication and session management flaws"""
        print("[+] Scanning for authentication vulnerabilities...")
        
        # Check for hardcoded credentials
        credential_patterns = [
            r'password\s*[:=]\s*[\'"][^\'"]{3,}[\'"]',
            r'api_key\s*[:=]\s*[\'"][^\'"]+[\'"]',
            r'secret\s*[:=]\s*[\'"][^\'"]+[\'"]',
            r'token\s*[:=]\s*[\'"][^\'"]+[\'"]',
            r'admin.*password',
        ]
        
        for pattern in credential_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'CRITICAL',
                    'Hardcoded Credentials',
                    'Hardcoded credentials found in source code',
                    match.group(),
                    'Remove hardcoded credentials and use environment variables'
                )

    def scan_xss_vulnerabilities(self, content):
        """Scan for Cross-Site Scripting vulnerabilities"""
        print("[+] Scanning for XSS vulnerabilities...")
        
        # Check for dangerous DOM manipulation
        xss_patterns = [
            r'innerHTML\s*=\s*[^;]+',
            r'document\.write\s*\(',
            r'eval\s*\(',
            r'setTimeout\s*\(\s*[\'"][^\'"]*[\'"]',
            r'setInterval\s*\(\s*[\'"][^\'"]*[\'"]',
            r'outerHTML\s*=',
        ]
        
        for pattern in xss_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'HIGH',
                    'XSS Vulnerability',
                    'Potential Cross-Site Scripting vulnerability',
                    match.group(),
                    'Sanitize user input and use safe DOM manipulation methods'
                )

    def scan_business_logic_flaws(self, content):
        """Scan for business logic vulnerabilities specific to gambling"""
        print("[+] Scanning for business logic flaws...")

        # Check for race condition vulnerabilities
        race_patterns = [
            r'setTimeout.*bet',
            r'setInterval.*spin',
            r'async.*bet.*await',
            r'Promise.*bet',
        ]

        for pattern in race_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'HIGH',
                    'Race Condition',
                    'Potential race condition in betting logic',
                    match.group(),
                    'Implement proper synchronization and server-side validation'
                )

        # Check for predictable game outcomes
        predictable_patterns = [
            r'seed\s*=\s*Date',
            r'Math\.random\(\)\s*\*\s*Date',
            r'new Date\(\).*random',
            r'timestamp.*random',
        ]

        for pattern in predictable_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'CRITICAL',
                    'Predictable Outcomes',
                    'Game outcomes may be predictable',
                    match.group(),
                    'Use cryptographically secure randomness with proper seeding'
                )

    def scan_network_security(self):
        """Scan for network security issues"""
        if not self.target_url:
            return

        print("[+] Scanning network security...")

        parsed_url = urlparse(self.target_url)
        hostname = parsed_url.hostname
        port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)

        # Check SSL/TLS configuration
        if parsed_url.scheme == 'https':
            try:
                context = ssl.create_default_context()
                with socket.create_connection((hostname, port), timeout=10) as sock:
                    with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                        cert = ssock.getpeercert()
                        cipher = ssock.cipher()

                        # Check for weak ciphers
                        if cipher and 'RC4' in cipher[0] or 'DES' in cipher[0]:
                            self.log_vulnerability(
                                'HIGH',
                                'Weak Cipher',
                                f'Weak cipher suite detected: {cipher[0]}',
                                f'Cipher: {cipher}',
                                'Configure server to use strong cipher suites only'
                            )

                        # Check certificate validity
                        if cert:
                            import datetime
                            not_after = datetime.datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                            days_until_expiry = (not_after - datetime.datetime.now()).days

                            if days_until_expiry < 30:
                                self.log_vulnerability(
                                    'MEDIUM',
                                    'Certificate Expiry',
                                    f'SSL certificate expires in {days_until_expiry} days',
                                    f'Expires: {cert["notAfter"]}',
                                    'Renew SSL certificate before expiration'
                                )

            except Exception as e:
                self.log_vulnerability(
                    'MEDIUM',
                    'SSL Configuration',
                    'Unable to verify SSL configuration',
                    str(e),
                    'Verify SSL/TLS configuration manually'
                )
        else:
            self.log_vulnerability(
                'HIGH',
                'Insecure Protocol',
                'Site not using HTTPS',
                f'Protocol: {parsed_url.scheme}',
                'Implement HTTPS with valid SSL certificate'
            )

    def scan_payment_security(self, content):
        """Scan for payment processing vulnerabilities"""
        print("[+] Scanning payment security...")

        # Check for payment-related patterns
        payment_patterns = [
            r'credit.*card',
            r'payment.*process',
            r'stripe.*key',
            r'paypal.*client',
            r'bitcoin.*address',
            r'wallet.*address',
            r'deposit.*amount',
            r'withdraw.*amount',
        ]

        for pattern in payment_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'HIGH',
                    'Payment Processing',
                    'Payment processing logic found in client-side code',
                    match.group(),
                    'Move all payment processing to secure server-side implementation'
                )

    def scan_data_exposure(self, content):
        """Scan for sensitive data exposure"""
        print("[+] Scanning for data exposure...")

        # Check for exposed sensitive data
        sensitive_patterns = [
            r'user.*id.*\d+',
            r'email.*@.*\.',
            r'phone.*\d{10}',
            r'address.*\d+.*street',
            r'ssn.*\d{3}-\d{2}-\d{4}',
            r'credit.*card.*\d{4}',
            r'private.*key',
            r'database.*connection',
        ]

        for pattern in sensitive_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'HIGH',
                    'Data Exposure',
                    'Potentially sensitive data found in client-side code',
                    match.group(),
                    'Remove sensitive data from client-side code'
                )

    def scan_local_files(self):
        """Scan local JavaScript/HTML files"""
        print(f"[+] Scanning {len(self.local_files)} local files...")
        
        for file_path in self.local_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    print(f"\n[+] Scanning file: {file_path}")
                    self.scan_all_vulnerabilities(content)
            except Exception as e:
                print(f"[-] Error reading file {file_path}: {e}")

    def scan_web_target(self):
        """Scan web target for vulnerabilities"""
        if not self.target_url:
            return
            
        print(f"[+] Scanning web target: {self.target_url}")
        
        try:
            # Get main page
            response = self.session.get(self.target_url, timeout=10)
            content = response.text

            # Scan main page content
            self.scan_all_vulnerabilities(content)

            # Scan network security
            self.scan_network_security()

            # Look for additional JavaScript files
            js_files = re.findall(r'<script[^>]+src=[\'"]([^\'"]+\.js)[\'"]', content)

            for js_file in js_files:
                js_url = urljoin(self.target_url, js_file)
                try:
                    js_response = self.session.get(js_url, timeout=5)
                    print(f"\n[+] Scanning JavaScript file: {js_url}")
                    self.scan_all_vulnerabilities(js_response.text)
                except Exception as e:
                    print(f"[-] Error fetching {js_url}: {e}")

        except Exception as e:
            print(f"[-] Error scanning web target: {e}")

    def scan_all_vulnerabilities(self, content):
        """Run all vulnerability scans on content"""
        self.scan_rng_vulnerabilities(content)
        self.scan_client_side_validation(content)
        self.scan_api_endpoints(content)
        self.scan_authentication_flaws(content)
        self.scan_xss_vulnerabilities(content)
        self.scan_business_logic_flaws(content)
        self.scan_payment_security(content)
        self.scan_data_exposure(content)

    def generate_report(self):
        """Generate comprehensive security report"""
        print("\n" + "="*80)
        print("SECURITY SCAN REPORT")
        print("="*80)
        
        if not self.vulnerabilities:
            print("✅ No vulnerabilities detected!")
            return
        
        # Group by severity
        severity_counts = {}
        for vuln in self.vulnerabilities:
            severity = vuln['severity']
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        print(f"\nVULNERABILITY SUMMARY:")
        for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
            count = severity_counts.get(severity, 0)
            if count > 0:
                print(f"  {severity}: {count}")
        
        # Save detailed report
        report_file = f"security_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.scan_results, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        # Generate recommendations
        self.generate_recommendations()

    def generate_recommendations(self):
        """Generate security recommendations"""
        recommendations = [
            "🔒 Implement server-side validation for all game logic",
            "🎲 Use cryptographically secure random number generators",
            "🛡️ Add proper authentication and authorization",
            "🔍 Implement input validation and sanitization",
            "📊 Add comprehensive logging and monitoring",
            "🔐 Use HTTPS for all communications",
            "⚡ Implement rate limiting for API endpoints",
            "🎯 Regular security audits and penetration testing"
        ]
        
        print("\n🛡️ SECURITY RECOMMENDATIONS:")
        for rec in recommendations:
            print(f"  {rec}")

def main():
    parser = argparse.ArgumentParser(description='Slot Game Security Scanner')
    parser.add_argument('-u', '--url', help='Target URL to scan')
    parser.add_argument('-f', '--files', nargs='+', help='Local files to scan')
    parser.add_argument('-o', '--output', help='Output report file')
    
    args = parser.parse_args()
    
    if not args.url and not args.files:
        print("Please provide either a URL (-u) or local files (-f) to scan")
        sys.exit(1)
    
    print("🎰 Slot Game Security Scanner v1.0")
    print("="*50)
    
    scanner = SlotSecurityScanner(args.url, args.files)
    
    if args.files:
        scanner.scan_local_files()
    
    if args.url:
        scanner.scan_web_target()
    
    scanner.generate_report()

if __name__ == "__main__":
    main()
