{"timestamp": "2025-07-23T13:50:57.215176", "target": "https://duelbits.com", "vulnerabilities": [{"severity": "MEDIUM", "type": "Missing Security <PERSON>", "description": "CSP not implemented", "evidence": "Missing header: Content-Security-Policy", "recommendation": "Implement Content-Security-Policy security header", "timestamp": "2025-07-23T13:50:57.615552"}, {"severity": "MEDIUM", "type": "Missing Security <PERSON>", "description": "Referrer policy not set", "evidence": "Missing header: <PERSON><PERSON>rer-Policy", "recommendation": "Implement Referrer-Policy security header", "timestamp": "2025-07-23T13:50:57.616593"}, {"severity": "HIGH", "type": "Token Exposure", "description": "Sensitive information exposed in client-side code", "evidence": "Found: token\":\"d82509ae68974a4489b6f4e444671cfb\"...", "recommendation": "Move sensitive data to server-side only", "timestamp": "2025-07-23T13:50:57.618308"}], "recommendations": []}