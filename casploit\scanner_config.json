{"scan_settings": {"timeout": 10, "max_threads": 5, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "follow_redirects": true, "verify_ssl": false}, "vulnerability_patterns": {"rng_vulnerabilities": ["Math\\.random\\(\\)", "new Date\\(\\)\\.getTime\\(\\)", "Date\\.now\\(\\)", "Math\\.floor\\(Math\\.random\\(\\)\\s*\\*\\s*\\d+\\)", "parseInt\\(Math\\.random\\(", "seed\\s*=\\s*\\d+", "srand\\(", "rand\\(\\)", "random\\.randint\\(", "random\\.choice\\("], "client_side_validation": ["balance\\s*[><=]+\\s*bet", "if\\s*\\(\\s*bet\\s*[><=]", "balance\\s*-=\\s*bet", "balance\\s*\\+=\\s*win", "localStorage\\.setItem.*balance", "sessionStorage\\.setItem.*balance", "document\\.cookie.*balance"], "api_endpoints": ["/api/[a-zA-Z0-9_/]+", "fetch\\(['\"]([^'\"]+)['\"]", "axios\\.[get|post|put|delete]+\\(['\"]([^'\"]+)['\"]", "XMLHttpRequest.*open\\(['\"][GET|POST]['\"],\\s*['\"]([^'\"]+)['\"]", "endpoint\\s*[:=]\\s*['\"]([^'\"]+)['\"]"], "authentication_flaws": ["password\\s*[:=]\\s*['\"][^'\"]{3,}['\"]", "api_key\\s*[:=]\\s*['\"][^'\"]+['\"]", "secret\\s*[:=]\\s*['\"][^'\"]+['\"]", "token\\s*[:=]\\s*['\"][^'\"]+['\"]", "admin.*password"], "xss_vulnerabilities": ["innerHTML\\s*=\\s*[^;]+", "document\\.write\\s*\\(", "eval\\s*\\(", "setTimeout\\s*\\(\\s*['\"][^'\"]*['\"]", "setInterval\\s*\\(\\s*['\"][^'\"]*['\"]", "outerHTML\\s*="], "business_logic_flaws": ["setTimeout.*bet", "setInterval.*spin", "async.*bet.*await", "Promise.*bet", "seed\\s*=\\s*Date", "Math\\.random\\(\\)\\s*\\*\\s*Date", "new Date\\(\\).*random", "timestamp.*random"], "payment_security": ["credit.*card", "payment.*process", "stripe.*key", "paypal.*client", "bitcoin.*address", "wallet.*address", "deposit.*amount", "withdraw.*amount"], "data_exposure": ["user.*id.*\\d+", "email.*@.*\\.", "phone.*\\d{10}", "address.*\\d+.*street", "ssn.*\\d{3}-\\d{2}-\\d{4}", "credit.*card.*\\d{4}", "private.*key", "database.*connection"]}, "sqli_payloads": ["'", "1' OR '1'='1", "'; DROP TABLE users; --", "1' UNION SELECT NULL--", "' OR 1=1--", "admin'--", "' OR 'a'='a", "1' AND 1=1--", "' WAITFOR DELAY '00:00:05'--"], "xss_payloads": ["<script>alert('XSS')</script>", "javascript:alert('XSS')", "<img src=x onerror=alert('XSS')>", "<svg onload=alert('XSS')>", "';alert('XSS');//", "\"><script>alert('XSS')</script>", "<iframe src=javascript:alert('XSS')></iframe>"], "common_endpoints": ["/api/user", "/api/balance", "/api/bet", "/api/spin", "/api/withdraw", "/api/deposit", "/admin", "/admin/login", "/api/admin", "/debug", "/test", "/api/test", "/config", "/api/config", "/stats", "/api/stats"], "file_extensions": [".js", ".html", ".php", ".asp", ".aspx", ".jsp", ".py", ".rb", ".go", ".java"], "severity_levels": {"CRITICAL": {"color": "red", "priority": 1, "description": "Immediate action required"}, "HIGH": {"color": "orange", "priority": 2, "description": "High risk vulnerability"}, "MEDIUM": {"color": "yellow", "priority": 3, "description": "Medium risk vulnerability"}, "LOW": {"color": "green", "priority": 4, "description": "Low risk vulnerability"}}, "recommendations": {"rng": ["Use cryptographically secure random number generators", "Implement server-side randomness generation", "Use proper seeding mechanisms", "Avoid predictable patterns in random generation"], "validation": ["Move all validation to server-side", "Implement proper input sanitization", "Use parameterized queries", "Validate all user inputs"], "authentication": ["Implement strong authentication mechanisms", "Use secure session management", "Implement proper authorization", "Use multi-factor authentication"], "network": ["Use HTTPS for all communications", "Implement proper SSL/TLS configuration", "Use strong cipher suites", "Implement certificate pinning"], "business_logic": ["Implement proper synchronization", "Use server-side game logic", "Implement rate limiting", "Add comprehensive logging"]}}