@echo off
echo ========================================
echo    Slot Game Security Scanner
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.6+ and try again
    pause
    exit /b 1
)

REM Install requirements if needed
if not exist "requirements_installed.flag" (
    echo Installing requirements...
    pip install -r requirements.txt
    if errorlevel 0 (
        echo. > requirements_installed.flag
        echo Requirements installed successfully
    ) else (
        echo ERROR: Failed to install requirements
        pause
        exit /b 1
    )
    echo.
)

REM Show menu
:menu
echo Choose an option:
echo 1. Scan a website URL
echo 2. Scan local files
echo 3. Run example demonstration
echo 4. View help
echo 5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto scan_url
if "%choice%"=="2" goto scan_files
if "%choice%"=="3" goto run_example
if "%choice%"=="4" goto show_help
if "%choice%"=="5" goto exit
echo Invalid choice. Please try again.
echo.
goto menu

:scan_url
echo.
set /p url="Enter the casino website URL to scan: "
if "%url%"=="" (
    echo No URL provided.
    goto menu
)
echo.
echo Scanning website: %url%
python slot_security_scanner.py -u "%url%"
echo.
echo Scan completed. Check the generated report file.
pause
goto menu

:scan_files
echo.
echo Enter the casino files to scan (space-separated):
echo Example: casino.js slots.html poker.php
set /p files="Files: "
if "%files%"=="" (
    echo No files provided.
    goto menu
)
echo.
echo Scanning files: %files%
python slot_security_scanner.py -f %files%
echo.
echo Scan completed. Check the generated report file.
pause
goto menu

:run_example
echo.
echo Running example demonstration...
python example_usage.py
echo.
pause
goto menu

:show_help
echo.
echo ========================================
echo           HELP INFORMATION
echo ========================================
echo.
echo This scanner helps identify security vulnerabilities in slot games and casino websites.
echo.
echo DETECTED VULNERABILITIES:
echo - Weak Random Number Generation (RNG)
echo - Client-side validation issues
echo - API security problems
echo - Authentication flaws
echo - Cross-Site Scripting (XSS)
echo - Business logic vulnerabilities
echo - Payment security issues
echo - Data exposure problems
echo.
echo USAGE EXAMPLES:
echo 1. Scan website: python slot_security_scanner.py -u https://casino.com
echo 2. Scan files: python slot_security_scanner.py -f casino.js slots.html
echo.
echo IMPORTANT LEGAL NOTICE:
echo Only scan websites and applications you own or have explicit permission to test.
echo Unauthorized scanning may be illegal in your jurisdiction.
echo.
echo FILES:
echo - slot_security_scanner.py: Main scanner
echo - payload_tester.py: Advanced testing
echo - scanner_config.json: Configuration
echo - README.md: Detailed documentation
echo.
pause
goto menu

:exit
echo.
echo Thank you for using Slot Game Security Scanner!
echo Remember to only scan your own applications or those you have permission to test.
pause
exit /b 0
