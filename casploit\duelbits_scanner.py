#!/usr/bin/env python3
"""
Duelbits.com Security Scanner
Specialized scanner for duelbits.com gambling platform
"""

import requests
import re
import json
import time
import random
import urllib.parse
from urllib.parse import urljoin, urlparse
from datetime import datetime
import ssl
import socket

class DuelbitsSecurityScanner:
    def __init__(self):
        self.target_url = "https://goated.com"
        self.session = requests.Session()
        
        # Use realistic headers to bypass basic protection
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        self.vulnerabilities = []
        self.scan_results = {
            'timestamp': datetime.now().isoformat(),
            'target': self.target_url,
            'vulnerabilities': [],
            'recommendations': []
        }

    def log_vulnerability(self, severity, vuln_type, description, evidence, recommendation):
        """Log a discovered vulnerability"""
        vulnerability = {
            'severity': severity,
            'type': vuln_type,
            'description': description,
            'evidence': evidence,
            'recommendation': recommendation,
            'timestamp': datetime.now().isoformat()
        }
        
        self.vulnerabilities.append(vulnerability)
        self.scan_results['vulnerabilities'].append(vulnerability)
        
        # Color coding for severity
        colors = {
            'CRITICAL': '\033[91m',  # Red
            'HIGH': '\033[93m',      # Yellow
            'MEDIUM': '\033[94m',    # Blue
            'LOW': '\033[92m',       # Green
            'RESET': '\033[0m'       # Reset
        }
        
        color = colors.get(severity, colors['RESET'])
        print(f"{color}[{severity}] {vuln_type}: {description}{colors['RESET']}")
        print(f"  Evidence: {evidence}")
        print(f"  Recommendation: {recommendation}\n")

    def test_initial_access(self):
        """Test initial access to duelbits.com"""
        print("[+] Testing initial access to duelbits.com...")
        
        try:
            response = self.session.get(self.target_url, timeout=15)
            print(f"[+] Status Code: {response.status_code}")
            print(f"[+] Response Size: {len(response.content)} bytes")
            
            # Check for common protection mechanisms
            if 'cloudflare' in response.text.lower():
                print("[!] Cloudflare protection detected")
            if 'ddos' in response.text.lower():
                print("[!] DDoS protection detected")
            if 'bot' in response.text.lower():
                print("[!] Bot protection detected")
                
            return response.text
            
        except requests.exceptions.RequestException as e:
            print(f"[-] Error accessing target: {e}")
            return ""

    def scan_network_security(self):
        """Scan network-level security configurations"""
        print("[+] Scanning network security...")
        
        parsed_url = urlparse(self.target_url)
        hostname = parsed_url.hostname
        port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
        
        # Check SSL/TLS configuration
        if parsed_url.scheme == 'https':
            try:
                context = ssl.create_default_context()
                with socket.create_connection((hostname, port), timeout=10) as sock:
                    with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                        cert = ssock.getpeercert()
                        cipher = ssock.cipher()

                        # Check for weak ciphers
                        if cipher and ('RC4' in cipher[0] or 'DES' in cipher[0]):
                            self.log_vulnerability(
                                'HIGH',
                                'Weak Cipher',
                                f'Weak cipher suite detected: {cipher[0]}',
                                f'Cipher: {cipher}',
                                'Configure server to use strong cipher suites only'
                            )
                        else:
                            print(f"[+] Strong cipher in use: {cipher[0] if cipher else 'Unknown'}")

                        # Check certificate validity
                        if cert:
                            not_after = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                            days_until_expiry = (not_after - datetime.now()).days
                            
                            if days_until_expiry < 30:
                                self.log_vulnerability(
                                    'MEDIUM',
                                    'Certificate Expiry',
                                    f'SSL certificate expires in {days_until_expiry} days',
                                    f'Expires: {cert["notAfter"]}',
                                    'Renew SSL certificate before expiration'
                                )
                            else:
                                print(f"[+] SSL certificate valid until: {cert['notAfter']}")

            except Exception as e:
                print(f"[-] SSL/TLS scan error: {e}")

    def test_common_vulnerabilities(self, content):
        """Test for common web vulnerabilities"""
        print("[+] Testing common vulnerabilities...")
        
        # Check for exposed sensitive information
        sensitive_patterns = [
            (r'api[_-]?key["\']?\s*[:=]\s*["\'][^"\']+["\']', 'API Key Exposure'),
            (r'secret[_-]?key["\']?\s*[:=]\s*["\'][^"\']+["\']', 'Secret Key Exposure'),
            (r'password["\']?\s*[:=]\s*["\'][^"\']+["\']', 'Password Exposure'),
            (r'token["\']?\s*[:=]\s*["\'][^"\']+["\']', 'Token Exposure'),
        ]
        
        for pattern, vuln_type in sensitive_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'HIGH',
                    vuln_type,
                    f'Sensitive information exposed in client-side code',
                    f'Found: {match.group()[:50]}...',
                    'Move sensitive data to server-side only'
                )

        # Check for XSS vulnerabilities
        xss_patterns = [
            r'innerHTML\s*=\s*[^;]+',
            r'document\.write\s*\(',
            r'eval\s*\(',
        ]
        
        for pattern in xss_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                self.log_vulnerability(
                    'MEDIUM',
                    'Potential XSS',
                    f'Potentially unsafe JavaScript pattern detected',
                    f'Pattern: {pattern}',
                    'Implement proper input sanitization and CSP'
                )

    def scan_slot_machine_vulnerabilities(self, content):
        """Comprehensive slot machine vulnerability scanning"""
        print("[+] Scanning for slot machine vulnerabilities...")

        # Check for weak RNG in slot machines
        self.scan_rng_vulnerabilities(content)

        # Check for client-side game logic
        self.scan_client_side_game_logic(content)

        # Check for predictable outcomes
        self.scan_predictable_outcomes(content)

        # Check for slot-specific business logic flaws
        self.scan_slot_business_logic(content)

    def scan_rng_vulnerabilities(self, content):
        """Scan for Random Number Generator vulnerabilities in slots"""
        print("[+] Scanning for RNG vulnerabilities...")

        # Check for weak RNG patterns
        weak_rng_patterns = [
            (r'Math\.random\(\)', 'JavaScript Math.random() is predictable'),
            (r'new Date\(\)\.getTime\(\)', 'Date-based seeding is predictable'),
            (r'Date\.now\(\)', 'Timestamp-based randomness is weak'),
            (r'Math\.floor\(Math\.random\(\)\s*\*\s*\d+\)', 'Simple Math.random() multiplication'),
            (r'parseInt\(Math\.random\(\)', 'parseInt with Math.random()'),
            (r'seed\s*=\s*\d+', 'Hardcoded seed values'),
            (r'srand\(', 'C-style srand() function'),
            (r'rand\(\)', 'C-style rand() function'),
            (r'random\.randint\(', 'Python randint without secure seeding'),
            (r'random\.choice\(', 'Python random.choice without secure seeding'),
        ]

        for pattern, description in weak_rng_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'CRITICAL',
                    'Weak RNG in Slots',
                    f'Slot machine uses weak RNG: {description}',
                    match.group(),
                    'Use cryptographically secure random number generators (crypto.getRandomValues(), secrets module)'
                )

    def scan_client_side_game_logic(self, content):
        """Scan for client-side game logic vulnerabilities"""
        print("[+] Scanning for client-side game logic...")

        # Check for client-side balance/bet validation
        validation_patterns = [
            (r'balance\s*[><=]+\s*bet', 'Balance validation on client'),
            (r'if\s*\(\s*bet\s*[><=]', 'Bet validation on client'),
            (r'balance\s*-=\s*bet', 'Balance deduction on client'),
            (r'balance\s*\+=\s*win', 'Win addition on client'),
            (r'localStorage\.setItem.*balance', 'Balance stored in localStorage'),
            (r'sessionStorage\.setItem.*balance', 'Balance stored in sessionStorage'),
            (r'document\.cookie.*balance', 'Balance stored in cookies'),
            (r'spin.*result.*client', 'Spin results calculated on client'),
            (r'reel.*position.*Math\.random', 'Reel positions determined on client'),
            (r'payline.*calculation.*client', 'Payline calculations on client'),
        ]

        for pattern, description in validation_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'CRITICAL',
                    'Client-Side Game Logic',
                    f'Critical slot game logic on client: {description}',
                    match.group(),
                    'Move ALL game logic to server-side with proper authentication'
                )

    def scan_predictable_outcomes(self, content):
        """Scan for predictable game outcomes"""
        print("[+] Scanning for predictable outcomes...")

        predictable_patterns = [
            (r'seed\s*=\s*Date', 'Date-based seeding'),
            (r'Math\.random\(\)\s*\*\s*Date', 'Date multiplication with random'),
            (r'new Date\(\).*random', 'Date object used with random'),
            (r'timestamp.*random', 'Timestamp-based randomness'),
            (r'getTime\(\).*seed', 'getTime() used for seeding'),
            (r'user.*id.*seed', 'User ID used as seed'),
            (r'session.*id.*random', 'Session ID affects randomness'),
            (r'sequential.*spin', 'Sequential spin patterns'),
        ]

        for pattern, description in predictable_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'CRITICAL',
                    'Predictable Slot Outcomes',
                    f'Slot outcomes may be predictable: {description}',
                    match.group(),
                    'Use cryptographically secure randomness with proper server-side seeding'
                )

    def scan_slot_business_logic(self, content):
        """Scan for slot-specific business logic flaws"""
        print("[+] Scanning for slot business logic flaws...")

        # Check for race condition vulnerabilities in slots
        race_patterns = [
            (r'setTimeout.*spin', 'Spin timing manipulation possible'),
            (r'setInterval.*reel', 'Reel timing manipulation possible'),
            (r'async.*spin.*await', 'Async spin logic race conditions'),
            (r'Promise.*bet.*spin', 'Promise-based betting race conditions'),
            (r'multiple.*spin.*same.*time', 'Multiple simultaneous spins'),
        ]

        for pattern, description in race_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'HIGH',
                    'Slot Race Condition',
                    f'Race condition in slot logic: {description}',
                    match.group(),
                    'Implement proper synchronization and server-side spin validation'
                )

        # Check for slot-specific manipulation patterns
        manipulation_patterns = [
            (r'autoplay.*unlimited', 'Unlimited autoplay possible'),
            (r'bet.*amount.*client.*control', 'Bet amount controlled by client'),
            (r'max.*bet.*bypass', 'Maximum bet bypass possible'),
            (r'free.*spin.*manipulation', 'Free spin manipulation'),
            (r'bonus.*round.*client', 'Bonus rounds handled on client'),
        ]

        for pattern, description in manipulation_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                self.log_vulnerability(
                    'HIGH',
                    'Slot Manipulation',
                    f'Slot manipulation possible: {description}',
                    match.group(),
                    'Implement server-side validation for all slot operations'
                )

    def test_gambling_specific_vulnerabilities(self):
        """Test for gambling-specific security issues"""
        print("[+] Testing gambling-specific vulnerabilities...")

        # Test common gambling endpoints
        gambling_endpoints = [
            '/api/bet',
            '/api/balance',
            '/api/withdraw',
            '/api/deposit',
            '/api/game',
            '/api/spin',
            '/api/user',
            '/api/wallet',
            '/api/slots',
            '/api/slot/spin',
            '/api/slot/bet',
            '/api/games/slots',
            '/slots/api',
            '/game/slots'
        ]

        for endpoint in gambling_endpoints:
            try:
                test_url = urljoin(self.target_url, endpoint)
                response = self.session.get(test_url, timeout=5)

                if response.status_code == 200:
                    print(f"[+] Found accessible endpoint: {endpoint}")

                    # Check for exposed data
                    if any(keyword in response.text.lower() for keyword in ['balance', 'amount', 'wallet', 'spin', 'reel']):
                        self.log_vulnerability(
                            'MEDIUM',
                            'Exposed Gambling Data',
                            f'Gambling endpoint may expose sensitive data: {endpoint}',
                            f'Status: {response.status_code}, Content contains financial/game data',
                            'Implement proper authentication and authorization'
                        )

            except Exception as e:
                pass  # Expected for most endpoints

    def analyze_security_headers(self):
        """Analyze HTTP security headers"""
        print("[+] Analyzing security headers...")
        
        try:
            response = self.session.head(self.target_url, timeout=10)
            headers = response.headers
            
            # Check for important security headers
            security_headers = {
                'Strict-Transport-Security': 'HSTS not implemented',
                'Content-Security-Policy': 'CSP not implemented',
                'X-Frame-Options': 'Clickjacking protection missing',
                'X-Content-Type-Options': 'MIME sniffing protection missing',
                'X-XSS-Protection': 'XSS protection header missing',
                'Referrer-Policy': 'Referrer policy not set'
            }
            
            for header, issue in security_headers.items():
                if header not in headers:
                    self.log_vulnerability(
                        'MEDIUM',
                        'Missing Security Header',
                        issue,
                        f'Missing header: {header}',
                        f'Implement {header} security header'
                    )
                else:
                    print(f"[+] {header}: {headers[header]}")
                    
        except Exception as e:
            print(f"[-] Error analyzing headers: {e}")

    def generate_duelbits_report(self):
        """Generate comprehensive security report for duelbits.com"""
        print("\n" + "="*80)
        print("DUELBITS.COM SECURITY SCAN REPORT")
        print("="*80)
        
        if not self.vulnerabilities:
            print("✅ No major vulnerabilities detected!")
            print("Note: Limited scan due to site protection mechanisms")
        else:
            # Group by severity
            severity_counts = {}
            for vuln in self.vulnerabilities:
                severity = vuln['severity']
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            print(f"\nVULNERABILITY SUMMARY:")
            for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
                count = severity_counts.get(severity, 0)
                if count > 0:
                    print(f"  {severity}: {count}")
        
        # Save detailed report
        report_file = f"duelbits_security_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.scan_results, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        # Duelbits-specific recommendations
        print("\n🛡️ DUELBITS.COM SECURITY RECOMMENDATIONS:")

        # General recommendations
        general_recommendations = [
            "🔒 Implement proper API authentication for gambling operations",
            "🛡️ Add comprehensive security headers (CSP, Referrer-Policy)",
            "🔍 Implement proper input validation",
            "📊 Add security monitoring and logging",
            "⚡ Review bot protection mechanisms",
            "🎯 Regular security audits for gambling compliance",
            "🔐 Implement strong user authentication"
        ]

        # Slot-specific recommendations
        slot_recommendations = [
            "🎰 Move ALL slot game logic to server-side",
            "🎲 Use cryptographically secure RNG for slot outcomes",
            "🔄 Implement server-side spin validation",
            "💰 Validate all bet amounts server-side",
            "🎯 Prevent race conditions in slot spinning",
            "🔒 Secure slot API endpoints with proper authentication",
            "📈 Monitor for unusual slot playing patterns",
            "🎮 Implement proper session management for slot games"
        ]

        print("\n  📋 GENERAL SECURITY:")
        for rec in general_recommendations:
            print(f"    {rec}")

        print("\n  🎰 SLOT MACHINE SPECIFIC:")
        for rec in slot_recommendations:
            print(f"    {rec}")

        # Check if slot-specific vulnerabilities were found
        slot_vulns = [v for v in self.vulnerabilities if any(keyword in v['type'].lower()
                     for keyword in ['slot', 'rng', 'game logic', 'predictable'])]

        if slot_vulns:
            print(f"\n  ⚠️  CRITICAL SLOT SECURITY ISSUES DETECTED: {len(slot_vulns)}")
            print("     Immediate attention required for slot machine security!")

    def scan_javascript_files(self, content):
        """Scan JavaScript files for slot machine vulnerabilities"""
        print("[+] Scanning JavaScript files for slot vulnerabilities...")

        # Look for JavaScript files
        js_files = re.findall(r'<script[^>]+src=[\'"]([^\'"]+\.js)[\'"]', content)

        # Also look for inline script tags
        inline_scripts = re.findall(r'<script[^>]*>(.*?)</script>', content, re.DOTALL)

        # Scan inline scripts
        for script in inline_scripts:
            if any(keyword in script.lower() for keyword in ['slot', 'spin', 'reel', 'bet', 'game']):
                print("[+] Found inline slot-related JavaScript")
                self.scan_slot_machine_vulnerabilities(script)

        # Scan external JavaScript files
        for js_file in js_files:
            try:
                js_url = urljoin(self.target_url, js_file)
                print(f"[+] Scanning JavaScript file: {js_url}")

                js_response = self.session.get(js_url, timeout=10)
                if js_response.status_code == 200:
                    js_content = js_response.text

                    # Check if this JS file contains slot-related code
                    if any(keyword in js_content.lower() for keyword in ['slot', 'spin', 'reel', 'bet', 'game', 'random']):
                        print(f"[+] Found slot-related code in: {js_file}")
                        self.scan_slot_machine_vulnerabilities(js_content)

            except Exception as e:
                print(f"[-] Error scanning {js_file}: {e}")

    def test_slot_specific_endpoints(self):
        """Test slot machine specific API endpoints"""
        print("[+] Testing slot-specific API endpoints...")

        slot_endpoints = [
            '/api/slots/spin',
            '/api/slots/bet',
            '/api/slots/balance',
            '/api/slots/games',
            '/api/game/slots',
            '/api/casino/slots',
            '/slots/api/spin',
            '/slots/api/bet',
            '/game/slot/spin',
            '/casino/slot/play',
            '/api/v1/slots',
            '/api/v2/slots',
            '/graphql',  # Many modern slot games use GraphQL
        ]

        for endpoint in slot_endpoints:
            try:
                test_url = urljoin(self.target_url, endpoint)

                # Test GET request
                response = self.session.get(test_url, timeout=5)
                if response.status_code in [200, 401, 403]:  # 401/403 means endpoint exists
                    print(f"[+] Found slot endpoint: {endpoint} (Status: {response.status_code})")

                    if response.status_code == 200:
                        # Check response content for slot data
                        if any(keyword in response.text.lower() for keyword in
                               ['reel', 'payline', 'symbol', 'jackpot', 'multiplier', 'freespin']):
                            self.log_vulnerability(
                                'HIGH',
                                'Exposed Slot API',
                                f'Slot API endpoint accessible: {endpoint}',
                                f'Status: {response.status_code}, Contains slot game data',
                                'Implement proper authentication for slot APIs'
                            )

                # Test POST with slot-specific payloads
                slot_payloads = [
                    {'bet': 100, 'lines': 25},
                    {'amount': 50, 'game': 'slots'},
                    {'spin': True, 'bet': 10},
                    {'autoplay': True, 'spins': 100},
                ]

                for payload in slot_payloads:
                    try:
                        post_response = self.session.post(test_url, json=payload, timeout=5)
                        if post_response.status_code == 200:
                            self.log_vulnerability(
                                'CRITICAL',
                                'Unauthenticated Slot API',
                                f'Slot API accepts requests without authentication: {endpoint}',
                                f'Payload: {payload}, Status: {post_response.status_code}',
                                'Implement proper authentication and authorization'
                            )
                    except:
                        pass

            except Exception as e:
                pass

    def run_comprehensive_scan(self):
        """Run comprehensive security scan of duelbits.com including slot machines"""
        print("🎰 Duelbits.com Security Scanner with Slot Machine Analysis")
        print("="*60)
        print("Target: https://duelbits.com")
        print("="*60)

        # Initial access test
        content = self.test_initial_access()

        # Network security scan
        self.scan_network_security()

        # Security headers analysis
        self.analyze_security_headers()

        # Common vulnerability tests
        self.test_common_vulnerabilities(content)

        # Slot machine specific scans
        print("\n" + "="*40)
        print("SLOT MACHINE SECURITY ANALYSIS")
        print("="*40)

        # Scan main page for slot vulnerabilities
        self.scan_slot_machine_vulnerabilities(content)

        # Scan JavaScript files for slot code
        self.scan_javascript_files(content)

        # Test slot-specific API endpoints
        self.test_slot_specific_endpoints()

        # General gambling-specific tests
        self.test_gambling_specific_vulnerabilities()

        # Generate report
        self.generate_duelbits_report()

def main():
    scanner = DuelbitsSecurityScanner()
    scanner.run_comprehensive_scan()

if __name__ == "__main__":
    main()
