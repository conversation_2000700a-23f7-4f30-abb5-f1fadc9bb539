#!/usr/bin/env python3
"""
Duelbits.com Security Scanner
Specialized scanner for duelbits.com gambling platform
"""

import requests
import re
import json
import time
import random
import urllib.parse
from urllib.parse import urljoin, urlparse
from datetime import datetime
import ssl
import socket

class DuelbitsSecurityScanner:
    def __init__(self):
        self.target_url = "https://duelbits.com"
        self.session = requests.Session()
        
        # Use realistic headers to bypass basic protection
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        self.vulnerabilities = []
        self.scan_results = {
            'timestamp': datetime.now().isoformat(),
            'target': self.target_url,
            'vulnerabilities': [],
            'recommendations': []
        }

    def log_vulnerability(self, severity, category, description, evidence=None, recommendation=None):
        """Log a discovered vulnerability"""
        vuln = {
            'severity': severity,
            'category': category,
            'description': description,
            'evidence': evidence,
            'recommendation': recommendation,
            'timestamp': datetime.now().isoformat()
        }
        self.vulnerabilities.append(vuln)
        self.scan_results['vulnerabilities'].append(vuln)
        
        colors = {
            'CRITICAL': '\033[91m',
            'HIGH': '\033[93m',
            'MEDIUM': '\033[94m',
            'LOW': '\033[92m',
            'END': '\033[0m'
        }
        
        print(f"{colors.get(severity, '')}{severity}: {category} - {description}{colors['END']}")
        if evidence:
            print(f"  Evidence: {evidence}")
        if recommendation:
            print(f"  Recommendation: {recommendation}")
        print()

    def test_initial_access(self):
        """Test initial access to winna.com"""
        print("[+] Testing initial access to winna.com...")
        
        try:
            response = self.session.get(self.target_url, timeout=15)
            print(f"[+] Response status: {response.status_code}")
            print(f"[+] Content length: {len(response.text)}")
            
            if response.status_code == 403:
                self.log_vulnerability(
                    'MEDIUM',
                    'Access Control',
                    'Site returns 403 Forbidden - may have bot protection',
                    f'Status: {response.status_code}',
                    'Verify if legitimate users can access the site'
                )
                
                # Try different approaches to bypass protection
                return self.bypass_protection()
            
            elif response.status_code == 200:
                print("[+] Successfully accessed winna.com")
                return response.text
            
            else:
                self.log_vulnerability(
                    'MEDIUM',
                    'Unusual Response',
                    f'Unexpected HTTP status code: {response.status_code}',
                    f'Status: {response.status_code}',
                    'Investigate unusual response codes'
                )
                return None
                
        except requests.exceptions.Timeout:
            self.log_vulnerability(
                'MEDIUM',
                'Performance Issue',
                'Site response timeout - potential DoS vulnerability',
                'Timeout after 15 seconds',
                'Implement proper timeout handling and rate limiting'
            )
            return None
            
        except requests.exceptions.SSLError as e:
            self.log_vulnerability(
                'HIGH',
                'SSL/TLS Issue',
                'SSL certificate or configuration problem',
                str(e),
                'Fix SSL/TLS configuration'
            )
            return None
            
        except Exception as e:
            print(f"[-] Error accessing site: {e}")
            return None

    def bypass_protection(self):
        """Attempt to bypass basic protection mechanisms"""
        print("[+] Attempting to bypass protection mechanisms...")
        
        # Try different user agents
        user_agents = [
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15',
            'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0 Firefox/88.0',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        ]
        
        for ua in user_agents:
            try:
                headers = self.session.headers.copy()
                headers['User-Agent'] = ua
                response = self.session.get(self.target_url, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    print(f"[+] Bypassed protection with User-Agent: {ua[:50]}...")
                    return response.text
                    
            except:
                continue
        
        # Try accessing common endpoints
        common_paths = [
            '/robots.txt',
            '/sitemap.xml',
            '/api',
            '/games',
            '/login',
            '/register'
        ]
        
        accessible_content = ""
        for path in common_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=5)
                
                if response.status_code == 200:
                    print(f"[+] Accessible endpoint found: {path}")
                    accessible_content += response.text
                    
                    if path == '/robots.txt':
                        self.analyze_robots_txt(response.text)
                        
            except:
                continue
        
        return accessible_content if accessible_content else None

    def analyze_robots_txt(self, robots_content):
        """Analyze robots.txt for information disclosure"""
        print("[+] Analyzing robots.txt...")
        
        # Look for disallowed paths that might reveal sensitive areas
        disallow_patterns = re.findall(r'Disallow:\s*(.+)', robots_content, re.IGNORECASE)
        
        sensitive_paths = []
        for path in disallow_patterns:
            path = path.strip()
            if any(keyword in path.lower() for keyword in ['admin', 'api', 'private', 'internal', 'test', 'dev']):
                sensitive_paths.append(path)
        
        if sensitive_paths:
            self.log_vulnerability(
                'MEDIUM',
                'Information Disclosure',
                'Robots.txt reveals potentially sensitive paths',
                f'Sensitive paths: {", ".join(sensitive_paths)}',
                'Review robots.txt to avoid revealing sensitive directories'
            )

    def scan_network_security(self):
        """Scan network security configuration"""
        print("[+] Scanning network security...")
        
        parsed_url = urlparse(self.target_url)
        hostname = parsed_url.hostname
        port = 443
        
        try:
            # Check SSL/TLS configuration
            context = ssl.create_default_context()
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    cipher = ssock.cipher()
                    
                    print(f"[+] SSL Cipher: {cipher[0] if cipher else 'Unknown'}")
                    
                    # Check for weak ciphers
                    if cipher and ('RC4' in cipher[0] or 'DES' in cipher[0] or 'MD5' in cipher[0]):
                        self.log_vulnerability(
                            'HIGH',
                            'Weak Cipher',
                            f'Weak cipher suite detected: {cipher[0]}',
                            f'Cipher: {cipher}',
                            'Configure server to use strong cipher suites only'
                        )
                    
                    # Check certificate validity
                    if cert:
                        import datetime
                        not_after = datetime.datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                        days_until_expiry = (not_after - datetime.datetime.now()).days
                        
                        print(f"[+] SSL Certificate expires in {days_until_expiry} days")
                        
                        if days_until_expiry < 30:
                            self.log_vulnerability(
                                'MEDIUM',
                                'Certificate Expiry',
                                f'SSL certificate expires in {days_until_expiry} days',
                                f'Expires: {cert["notAfter"]}',
                                'Renew SSL certificate before expiration'
                            )
                        
                        # Check certificate issuer
                        issuer = dict(x[0] for x in cert['issuer'])
                        print(f"[+] Certificate issuer: {issuer.get('organizationName', 'Unknown')}")
                        
        except Exception as e:
            self.log_vulnerability(
                'MEDIUM',
                'SSL Analysis Failed',
                'Unable to analyze SSL configuration',
                str(e),
                'Manually verify SSL/TLS configuration'
            )

    def test_common_vulnerabilities(self, content=None):
        """Test for common web vulnerabilities"""
        print("[+] Testing for common vulnerabilities...")
        
        # Test common endpoints for information disclosure
        test_endpoints = [
            '/.env',
            '/config.json',
            '/api/config',
            '/admin',
            '/phpmyadmin',
            '/wp-admin',
            '/debug',
            '/test',
            '/api/users',
            '/api/admin',
            '/backup',
            '/.git',
            '/database.sql'
        ]
        
        for endpoint in test_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                response = self.session.get(url, timeout=5)
                
                if response.status_code == 200:
                    self.log_vulnerability(
                        'HIGH',
                        'Information Disclosure',
                        f'Sensitive endpoint accessible: {endpoint}',
                        f'Status: {response.status_code}, Size: {len(response.text)}',
                        f'Secure or remove access to {endpoint}'
                    )
                elif response.status_code == 403:
                    print(f"[+] {endpoint} - Protected (403)")
                elif response.status_code == 401:
                    self.log_vulnerability(
                        'MEDIUM',
                        'Authentication Required',
                        f'Endpoint requires authentication: {endpoint}',
                        f'Status: {response.status_code}',
                        'Ensure proper authentication is implemented'
                    )
                    
            except:
                continue
            
            time.sleep(0.1)  # Rate limiting

    def test_gambling_specific_vulnerabilities(self):
        """Test for gambling-specific vulnerabilities"""
        print("[+] Testing gambling-specific vulnerabilities...")
        
        # Test for common gambling API endpoints
        gambling_endpoints = [
            '/api/bet',
            '/api/spin',
            '/api/balance',
            '/api/withdraw',
            '/api/deposit',
            '/api/games',
            '/api/jackpot',
            '/api/user/balance',
            '/api/user/bets',
            '/api/game/result'
        ]
        
        for endpoint in gambling_endpoints:
            try:
                url = urljoin(self.target_url, endpoint)
                
                # Test GET request
                response = self.session.get(url, timeout=5)
                if response.status_code == 200:
                    self.log_vulnerability(
                        'HIGH',
                        'Exposed Gambling API',
                        f'Gambling API endpoint accessible without authentication: {endpoint}',
                        f'Status: {response.status_code}',
                        'Implement proper authentication for gambling APIs'
                    )
                
                # Test POST with common parameters
                test_data = {
                    'amount': 100,
                    'bet': 50,
                    'game': 'slots'
                }
                
                response = self.session.post(url, data=test_data, timeout=5)
                if response.status_code == 200:
                    self.log_vulnerability(
                        'CRITICAL',
                        'Gambling API Vulnerability',
                        f'Gambling API accepts requests without authentication: {endpoint}',
                        f'POST Status: {response.status_code}',
                        'Implement strict authentication and validation for gambling operations'
                    )
                    
            except:
                continue
            
            time.sleep(0.2)

    def analyze_security_headers(self):
        """Analyze security headers"""
        print("[+] Analyzing security headers...")
        
        try:
            response = self.session.head(self.target_url, timeout=10)
            headers = response.headers
            
            # Check for important security headers
            security_headers = {
                'Strict-Transport-Security': 'HSTS not implemented',
                'Content-Security-Policy': 'CSP not implemented',
                'X-Frame-Options': 'Clickjacking protection missing',
                'X-Content-Type-Options': 'MIME sniffing protection missing',
                'X-XSS-Protection': 'XSS protection header missing',
                'Referrer-Policy': 'Referrer policy not set'
            }
            
            for header, issue in security_headers.items():
                if header not in headers:
                    self.log_vulnerability(
                        'MEDIUM',
                        'Missing Security Header',
                        issue,
                        f'Missing header: {header}',
                        f'Implement {header} security header'
                    )
                else:
                    print(f"[+] {header}: {headers[header]}")
                    
        except Exception as e:
            print(f"[-] Error analyzing headers: {e}")

    def generate_duelbits_report(self):
        """Generate comprehensive security report for duelbits.com"""
        print("\n" + "="*80)
        print("DUELBITS.COM SECURITY SCAN REPORT")
        print("="*80)

        if not self.vulnerabilities:
            print("✅ No major vulnerabilities detected!")
            print("Note: Limited scan due to site protection mechanisms")
        else:
            # Group by severity
            severity_counts = {}
            for vuln in self.vulnerabilities:
                severity = vuln['severity']
                severity_counts[severity] = severity_counts.get(severity, 0) + 1

            print(f"\nVULNERABILITY SUMMARY:")
            for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
                count = severity_counts.get(severity, 0)
                if count > 0:
                    print(f"  {severity}: {count}")

        # Save detailed report
        report_file = f"duelbits_security_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.scan_results, f, indent=2)

        print(f"\n📄 Detailed report saved to: {report_file}")

        # Duelbits-specific recommendations
        print("\n🛡️ DUELBITS.COM SECURITY RECOMMENDATIONS:")
        recommendations = [
            "🔒 Implement proper API authentication for gambling operations",
            "🎲 Ensure all game logic is server-side only",
            "🛡️ Add comprehensive security headers",
            "🔍 Implement proper input validation",
            "📊 Add security monitoring and logging",
            "⚡ Review bot protection mechanisms",
            "🎯 Regular security audits for gambling compliance",
            "🔐 Implement strong user authentication"
        ]

        for rec in recommendations:
            print(f"  {rec}")

    def run_comprehensive_scan(self):
        """Run comprehensive security scan of duelbits.com"""
        print("🎰 Duelbits.com Security Scanner")
        print("="*50)
        print("Target: https://duelbits.com")
        print("="*50)

        # Initial access test
        content = self.test_initial_access()

        # Network security scan
        self.scan_network_security()

        # Security headers analysis
        self.analyze_security_headers()

        # Common vulnerability tests
        self.test_common_vulnerabilities(content)

        # Gambling-specific tests
        self.test_gambling_specific_vulnerabilities()

        # Generate report
        self.generate_duelbits_report()

def main():
    scanner = DuelbitsSecurityScanner()
    scanner.run_comprehensive_scan()

if __name__ == "__main__":
    main()
