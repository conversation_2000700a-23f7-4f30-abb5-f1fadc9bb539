#!/usr/bin/env python3
"""
Quick security scan for goated.com
Focuses on main page analysis without heavy JavaScript processing
"""

import requests
import re
import json
from datetime import datetime
import urllib.parse
from urllib.parse import urljoin, urlparse

def quick_scan_goated():
    target_url = "https://goated.com"
    vulnerabilities = []
    
    print("🎰 Quick Security Scan for goated.com")
    print("="*50)
    
    try:
        # Get main page
        print("[+] Fetching main page...")
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        response = session.get(target_url, timeout=10)
        content = response.text
        print(f"[+] Retrieved {len(content)} characters from main page")
        
        # Quick vulnerability checks
        print("[+] Scanning for vulnerabilities...")
        
        # Check for weak RNG patterns
        weak_rng_patterns = [
            r'Math\.random\(\)',
            r'new Date\(\)\.getTime\(\)',
            r'Date\.now\(\)',
            r'Math\.floor\(Math\.random\(\)\s*\*\s*\d+\)',
        ]
        
        for pattern in weak_rng_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                vulnerabilities.append({
                    'severity': 'HIGH',
                    'category': 'Weak RNG',
                    'description': 'Potentially predictable random number generation detected',
                    'evidence': match.group(),
                    'recommendation': 'Use cryptographically secure random number generators'
                })
                print(f"🔴 HIGH: Weak RNG - {match.group()}")
        
        # Check for client-side validation
        validation_patterns = [
            r'balance\s*[><=]+\s*bet',
            r'if\s*\(\s*bet\s*[><=]',
            r'balance\s*-=\s*bet',
            r'balance\s*\+=\s*win',
            r'localStorage\.setItem.*balance',
        ]
        
        for pattern in validation_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                vulnerabilities.append({
                    'severity': 'CRITICAL',
                    'category': 'Client-Side Validation',
                    'description': 'Critical game logic handled on client-side',
                    'evidence': match.group(),
                    'recommendation': 'Move all balance and bet validation to server-side'
                })
                print(f"🔴 CRITICAL: Client-Side Validation - {match.group()}")
        
        # Check for hardcoded credentials
        credential_patterns = [
            r'password\s*[:=]\s*[\'"][^\'"]{3,}[\'"]',
            r'api_key\s*[:=]\s*[\'"][^\'"]+[\'"]',
            r'secret\s*[:=]\s*[\'"][^\'"]+[\'"]',
            r'token\s*[:=]\s*[\'"][^\'"]+[\'"]',
        ]
        
        for pattern in credential_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                vulnerabilities.append({
                    'severity': 'CRITICAL',
                    'category': 'Hardcoded Credentials',
                    'description': 'Hardcoded credentials found in source code',
                    'evidence': match.group(),
                    'recommendation': 'Remove hardcoded credentials and use environment variables'
                })
                print(f"🔴 CRITICAL: Hardcoded Credentials - {match.group()}")
        
        # Check for XSS vulnerabilities
        xss_patterns = [
            r'innerHTML\s*=\s*[^;]+',
            r'document\.write\s*\(',
            r'eval\s*\(',
        ]
        
        for pattern in xss_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                vulnerabilities.append({
                    'severity': 'HIGH',
                    'category': 'XSS Vulnerability',
                    'description': 'Potential Cross-Site Scripting vulnerability',
                    'evidence': match.group(),
                    'recommendation': 'Sanitize user input and use safe DOM manipulation methods'
                })
                print(f"🟡 HIGH: XSS Vulnerability - {match.group()}")
        
        # Check for API endpoints
        api_patterns = [
            r'/api/[a-zA-Z0-9_/]+',
            r'fetch\([\'"]([^\'"]+)[\'"]',
            r'endpoint\s*[:=]\s*[\'"]([^\'"]+)[\'"]',
        ]
        
        endpoints = set()
        for pattern in api_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                endpoint = match.group(1) if match.groups() else match.group()
                endpoints.add(endpoint)
                print(f"🔍 Found API endpoint: {endpoint}")
        
        # Check HTTPS
        parsed_url = urlparse(target_url)
        if parsed_url.scheme != 'https':
            vulnerabilities.append({
                'severity': 'HIGH',
                'category': 'Insecure Protocol',
                'description': 'Site not using HTTPS',
                'evidence': f'Protocol: {parsed_url.scheme}',
                'recommendation': 'Implement HTTPS with valid SSL certificate'
            })
            print(f"🔴 HIGH: Insecure Protocol - {parsed_url.scheme}")
        else:
            print(f"✅ HTTPS is enabled")
        
        # Generate report
        print("\n" + "="*50)
        print("SCAN RESULTS")
        print("="*50)
        
        if not vulnerabilities:
            print("✅ No major vulnerabilities detected in main page!")
        else:
            severity_counts = {}
            for vuln in vulnerabilities:
                severity = vuln['severity']
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            print(f"\nVULNERABILITY SUMMARY:")
            for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
                count = severity_counts.get(severity, 0)
                if count > 0:
                    print(f"  {severity}: {count}")
        
        # Save report
        report = {
            'timestamp': datetime.now().isoformat(),
            'target': target_url,
            'vulnerabilities': vulnerabilities,
            'scan_type': 'quick_scan'
        }
        
        report_file = f"goated_security_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Report saved to: {report_file}")
        
        # Recommendations
        print("\n🛡️ SECURITY RECOMMENDATIONS:")
        recommendations = [
            "🔒 Implement server-side validation for all game logic",
            "🎲 Use cryptographically secure random number generators",
            "🛡️ Add proper authentication and authorization",
            "🔍 Implement input validation and sanitization",
            "📊 Add comprehensive logging and monitoring",
            "⚡ Implement rate limiting for API endpoints",
            "🎯 Regular security audits and penetration testing"
        ]
        
        for rec in recommendations:
            print(f"  {rec}")
            
    except Exception as e:
        print(f"[-] Error during scan: {e}")

if __name__ == "__main__":
    quick_scan_goated()
